import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import { <PERSON><PERSON>erRouter } from "react-router-dom";
import { Toaster } from 'sonner';
import App from "./App.tsx";
import "./index.css";

// Derive a basename that works when opening built index.html from a sub-path
function getBaseName() {
  // Prefer Vite's BASE_URL if it's an absolute path like "/foo".
  const base = import.meta.env.BASE_URL as string | undefined;
  if (base && base !== "/" && base.startsWith("/")) {
    return base.replace(/\/$/, "");
  }
  // If the current path ends with /index.html, use its directory as basename.
  const path = window.location.pathname;
  if (/\/index\.html$/.test(path)) {
    return path.replace(/\/index\.html$/, "");
  }
  return "/";
}

const basename = getBaseName();

createRoot(document.getElementById("root")!).render(
  <StrictMode>
    <BrowserRouter basename={basename}>
      <App />
      <Toaster />
    </BrowserRouter>
  </StrictMode>
);
