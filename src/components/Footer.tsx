import { motion } from 'framer-motion';

export default function Footer() {
  return (
    <footer className="bg-gray-900 text-white pt-16 pb-8">
      <div className="container mx-auto px-4 max-w-6xl">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
          <div>
            <h3 className="text-xl font-bold mb-4 flex items-center">
              <i className="fa-solid fa-mountain mr-2 text-blue-400"></i>青海旅游
            </h3>
            <p className="text-gray-400 text-sm mb-4">
              探索青海的自然美景和人文魅力，体验高原独特的风土人情。
            </p>
            <div className="flex space-x-4">
              <a href="#" className="text-gray-400 hover:text-white transition-colors">
                <i className="fa-brands fa-weixin text-xl"></i>
              </a>
              <a href="#" className="text-gray-400 hover:text-white transition-colors">
                <i className="fa-brands fa-weibo text-xl"></i>
              </a>
              <a href="#" className="text-gray-400 hover:text-white transition-colors">
                <i className="fa-brands fa-qq text-xl"></i>
              </a>
            </div>
          </div>
          
          <div>
            <h4 className="text-lg font-bold mb-4">快速链接</h4>
            <ul className="space-y-2 text-gray-400">
              <li><a href="#" className="hover:text-white transition-colors">首页</a></li>
              <li><a href="#" className="hover:text-white transition-colors">旅游景点</a></li>
              <li><a href="#" className="hover:text-white transition-colors">美食文化</a></li>
              <li><a href="#" className="hover:text-white transition-colors">人文故事</a></li>
              <li><a href="#" className="hover:text-white transition-colors">旅游攻略</a></li>
            </ul>
          </div>
          
          <div>
            <h4 className="text-lg font-bold mb-4">联系我们</h4>
            <ul className="space-y-2 text-gray-400">
              <li className="flex items-start">
                <i className="fa-solid fa-map-marker-alt mt-1 mr-2 text-blue-400"></i>
                <span>青海省西宁市城西区黄河路158号</span>
              </li>
              <li className="flex items-center">
                <i className="fa-solid fa-phone mr-2 text-blue-400"></i>
                <span>0971-1234567</span>
              </li>
              <li className="flex items-center">
                <i className="fa-solid fa-envelope mr-2 text-blue-400"></i>
                <span><EMAIL></span>
              </li>
            </ul>
          </div>
          
          <div>
            <h4 className="text-lg font-bold mb-4">订阅资讯</h4>
            <p className="text-gray-400 text-sm mb-4">
              订阅我们的 newsletter，获取最新的旅游资讯和优惠信息。
            </p>
            <div className="flex">
              <input 
                type="email" 
                placeholder="您的邮箱地址" 
                className="px-4 py-2 rounded-l-lg w-full bg-gray-800 border border-gray-700 text-white focus:outline-none focus:ring-1 focus:ring-blue-500"
              />
              <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 rounded-r-lg transition-colors">
                <i className="fa-solid fa-paper-plane"></i>
              </button>
            </div>
          </div>
        </div>
        
        <div className="border-t border-gray-800 pt-8 text-center text-gray-500 text-sm">
          <p>© 2025 青海旅游网. 保留所有权利.</p>
        </div>
      </div>
    </footer>
  );
}