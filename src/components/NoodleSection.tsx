import { motion } from 'framer-motion';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, Ta<PERSON>Trigger } from '@/components/ui/Tabs';

// 拉面特色数据
const noodleFeatures = [
  {
    title: '手工拉制',
    description: '青海拉面采用传统手工拉制工艺，经过和面、醒面、溜条、拉面等多道工序，面条劲道有弹性。',
    icon: 'fa-hand-sparkles'
  },
  {
    title: '汤头鲜美',
    description: '汤底以牛骨或羊骨慢火熬制数小时而成，汤色清亮，味道醇厚，营养丰富。',
    icon: 'fa-fire-flame-curved'
  },
  {
    title: '配料丰富',
    description: '拉面配以牛肉片、萝卜、香菜和蒜苗等食材，色彩鲜艳，营养均衡，口感丰富。',
    icon: 'fa-carrot'
  },
  {
    title: '风味独特',
    description: '青海拉面融合了多民族饮食文化特色，形成了独特的酸辣口味，开胃爽口。',
    icon: 'fa-star'
  }
];

// 拉面种类数据
const noodleTypes = [
  {
    name: '牛肉面',
    description: '最经典的青海拉面，以鲜嫩的牛肉片和醇厚的牛骨汤为特色，面条可根据喜好选择粗细。',
    imageUrl: 'https://space.coze.cn/api/coze_space/gen_image?image_size=landscape_16_9&prompt=Qinghai%20beef%20noodle%20soup%20delicious%20food&sign=c04ea373282e3992de1c195e53c54339'
  },
  {
    name: '羊肉面',
    description: '采用青海本地羊肉，肉质鲜嫩无膻味，汤色乳白，冬季食用有驱寒暖身之效。',
    imageUrl: 'https://space.coze.cn/api/coze_space/gen_image?image_size=landscape_16_9&prompt=Qinghai%20mutton%20noodle%20soup%20traditional%20food&sign=6f5fafdf63d575fd6e25f88160ee1fb2'
  },
  {
    name: '酸菜面',
    description: '加入本地腌制的酸菜，酸辣开胃，特别适合夏季食用，能增进食欲，解腻爽口。',
    imageUrl: 'https://space.coze.cn/api/coze_space/gen_image?image_size=landscape_16_9&prompt=Qinghai%20sour%20cabbage%20noodle%20soup%20local%20specialty&sign=4f6a2c317ccdec8dc53a2a09fceb4d45'
  },
  {
    name: '干拌面',
    description: '无汤版本的拉面，面条拌以特制酱料和肉末，口感浓郁，适合喜欢重口味的食客。',
    imageUrl: 'https://space.coze.cn/api/coze_space/gen_image?image_size=landscape_16_9&prompt=Qinghai%20dry%20mixed%20noodle%20with%20sauce&sign=edc3ceaade431f6409e9d6df73b22451'
  }
];

export default function NoodleSection() {
  return (
    <section className="py-20 px-4 bg-gradient-to-b from-white to-orange-50">
      <div className="container mx-auto max-w-6xl">
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-bold text-gray-800 mb-4">
            <i className="fa-solid fa-utensils text-amber-600 mr-2"></i>青海拉面特色
          </h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            青海拉面是青海饮食文化的代表，融合了多民族的烹饪技艺，以其劲道的面条、鲜美的汤头和独特的风味享誉全国。
          </p>
        </motion.div>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center mb-20">
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <div className="relative rounded-2xl overflow-hidden shadow-xl">
              <img 
                src="https://space.coze.cn/api/coze_space/gen_image?image_size=landscape_16_9&prompt=Qinghai%20traditional%20handmade%20noodle%20making%20process&sign=93fb1ae0c521b0564611e1d84da887ee" 
                alt="青海拉面制作过程"
                className="w-full h-[400px] object-cover"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent flex items-end">
                <div className="p-6 text-white">
                  <h3 className="text-2xl font-bold mb-2">传统手工拉面技艺</h3>
                  <p className="text-white/90">传承百年的手工拉制工艺，每一根面条都凝聚着匠人的心血</p>
                </div>
              </div>
            </div>
          </motion.div>
          
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h3 className="text-2xl font-bold text-gray-800 mb-6">拉面的独特之处</h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              {noodleFeatures.map((feature, index) => (
                <div 
                  key={index} 
                  className="bg-white p-4 rounded-xl shadow-md hover:shadow-lg transition-shadow duration-300"
                >
                  <div className="text-amber-600 text-2xl mb-3">
                    <i className={`fa-solid ${feature.icon}`}></i>
                  </div>
                  <h4 className="font-bold text-gray-800 mb-2">{feature.title}</h4>
                  <p className="text-gray-600 text-sm">{feature.description}</p>
                </div>
              ))}
            </div>
            
            <div className="mt-8 bg-amber-50 border-l-4 border-amber-500 p-4 rounded-r-lg">
              <h4 className="font-bold text-gray-800 mb-2 flex items-center">
                <i className="fa-solid fa-lightbulb text-amber-500 mr-2"></i>
                拉面小知识
              </h4>
              <p className="text-gray-600 text-sm">
                青海拉面讲究"一清二白三红四绿五黄"：一清指汤清，二白指萝卜白，三红指辣椒油红，四绿指香菜蒜苗绿，五黄指面条黄亮。
              </p>
            </div>
          </motion.div>
        </div>
        
         <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="mb-12"
          >
            <div className="relative rounded-2xl overflow-hidden shadow-xl">
              <video
                src="https://images.wosaimg.com/uploadVideo/MWZkZGUyMzgxOWFiNTlhODkxN2U5MzE5NDk5ZTFmOTAt5byg6buO5pWPLTIwMjUtOC0yMiAwOjI3OjM5.mp4"
                autoPlay
                muted
                playsInline
                loop
                className="w-full h-[300px] object-cover"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/40 to-transparent flex items-end">
                <div className="p-6 text-white">
                  <h3 className="text-xl font-bold mb-1">青海拉面制作工艺</h3>
                  <p className="text-white/90 text-sm">传统手工拉面的制作全过程展示</p>
                </div>
              </div>
            </div>
          </motion.div>

         <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <h3 className="text-2xl font-bold text-gray-800 mb-6 text-center">青海拉面种类</h3>
          
          <Tabs defaultValue="beef" className="w-full">
            <TabsList className="grid grid-cols-4 mb-6">
              <TabsTrigger value="beef">牛肉面</TabsTrigger>
              <TabsTrigger value="mutton">羊肉面</TabsTrigger>
              <TabsTrigger value="sour">酸菜面</TabsTrigger>
              <TabsTrigger value="dry">干拌面</TabsTrigger>
            </TabsList>
            
            <div className="bg-white rounded-xl shadow-lg overflow-hidden">
              <TabsContent value="beef" className="m-0">
                <div className="grid grid-cols-1 md:grid-cols-2">
                  <div className="h-64 md:h-auto">
                    <img 
                      src={noodleTypes[0].imageUrl} 
                      alt={noodleTypes[0].name}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <div className="p-6">
                    <h4 className="text-xl font-bold text-gray-800 mb-2">{noodleTypes[0].name}</h4>
                    <p className="text-gray-600">{noodleTypes[0].description}</p>
                    <div className="mt-4 flex items-center text-amber-600">
                      <i className="fa-solid fa-star"></i>
                      <i className="fa-solid fa-star"></i>
                      <i className="fa-solid fa-star"></i>
                      <i className="fa-solid fa-star"></i>
                      <i className="fa-solid fa-star-half-stroke"></i>
                      <span className="ml-2 text-gray-700 font-medium">4.8/5</span>
                    </div>
                  </div>
                </div>
              </TabsContent>
              
              <TabsContent value="mutton" className="m-0">
                <div className="grid grid-cols-1 md:grid-cols-2">
                  <div className="h-64 md:h-auto">
                    <img 
                      src={noodleTypes[1].imageUrl} 
                      alt={noodleTypes[1].name}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <div className="p-6">
                    <h4 className="text-xl font-bold text-gray-800 mb-2">{noodleTypes[1].name}</h4>
                    <p className="text-gray-600">{noodleTypes[1].description}</p>
                    <div className="mt-4 flex items-center text-amber-600">
                      <i className="fa-solid fa-star"></i>
                      <i className="fa-solid fa-star"></i>
                      <i className="fa-solid fa-star"></i>
                      <i className="fa-solid fa-star"></i>
                      <i className="fa-solid fa-star"></i>
                      <span className="ml-2 text-gray-700 font-medium">4.9/5</span>
                    </div>
                  </div>
                </div>
              </TabsContent>
              
              <TabsContent value="sour" className="m-0">
                <div className="grid grid-cols-1 md:grid-cols-2">
                  <div className="h-64 md:h-auto">
                    <img 
                      src={noodleTypes[2].imageUrl} 
                      alt={noodleTypes[2].name}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <div className="p-6">
                    <h4 className="text-xl font-bold text-gray-800 mb-2">{noodleTypes[2].name}</h4>
                    <p className="text-gray-600">{noodleTypes[2].description}</p>
                    <div className="mt-4 flex items-center text-amber-600">
                      <i className="fa-solid fa-star"></i>
                      <i className="fa-solid fa-star"></i>
                      <i className="fa-solid fa-star"></i>
                      <i className="fa-solid fa-star"></i>
                      <i className="fa-regular fa-star"></i>
                      <span className="ml-2 text-gray-700 font-medium">4.2/5</span>
                    </div>
                  </div>
                </div>
              </TabsContent>
              
              <TabsContent value="dry" className="m-0">
                <div className="grid grid-cols-1 md:grid-cols-2">
                  <div className="h-64 md:h-auto">
                    <img 
                      src={noodleTypes[3].imageUrl} 
                      alt={noodleTypes[3].name}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <div className="p-6">
                    <h4 className="text-xl font-bold text-gray-800 mb-2">{noodleTypes[3].name}</h4>
                    <p className="text-gray-600">{noodleTypes[3].description}</p>
                    <div className="mt-4 flex items-center text-amber-600">
                      <i className="fa-solid fa-star"></i>
                      <i className="fa-solid fa-star"></i>
                      <i className="fa-solid fa-star"></i>
                      <i className="fa-solid fa-star"></i>
                      <i className="fa-solid fa-star-half-stroke"></i>
                      <span className="ml-2 text-gray-700 font-medium">4.7/5</span>
                    </div>
                  </div>
                </div>
              </TabsContent>
            </div>
          </Tabs>
        </motion.div>
      </div>
    </section>
  );
}