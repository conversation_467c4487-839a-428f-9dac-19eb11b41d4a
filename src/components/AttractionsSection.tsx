import { useState, useEffect } from 'react';
import { motion, useInView } from 'framer-motion';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/Card';

// 旅游景点数据
const attractionsData = [
  {
    id: 1,
    name: '青海湖',
    description: '中国最大的内陆湖，湖水湛蓝如宝石，环湖四周草原广袤，景色壮丽。夏季油菜花海与湖水交相辉映，美不胜收。',
    imageUrl: 'https://space.coze.cn/api/coze_space/gen_image?image_size=landscape_16_9&prompt=Qinghai%20Lake%20blue%20water%20scenic%20view&sign=78f03953b701a805ffef1e3b0ba291c5',
    location: '青海省海南藏族自治州',
    bestSeason: '6-8月'
  },
  {
    id: 2,
    name: '茶卡盐湖',
    description: '被誉为"天空之镜"，湖面晶莹剔透，倒映蓝天白云，形成水天一色的绝美景观。游客可赤脚漫步湖面，体验天空之镜的神奇。',
    imageUrl: 'https://space.coze.cn/api/coze_space/gen_image?image_size=landscape_16_9&prompt=Chaka%20Salt%20Lake%20mirror%20effect%20scenery&sign=1f2859ee4be4078a8012aa8134f8f44e',
    location: '青海省海西蒙古族藏族自治州',
    bestSeason: '7-9月'
  },
  {
    id: 3,
    name: '塔尔寺',
    description: '藏传佛教格鲁派六大寺院之一，建筑宏伟壮观，酥油花、壁画和堆绣被誉为"塔尔寺三绝"，具有极高的艺术和宗教价值。',
    imageUrl: 'https://space.coze.cn/api/coze_space/gen_image?image_size=landscape_16_9&prompt=Taer%20Monastery%20traditional%20Tibetan%20architecture&sign=b61ebe4ff0061926b901ca84c109b15e',
    location: '青海省西宁市湟中区',
    bestSeason: '全年'
  },
  {
    id: 4,
    name: '祁连山草原',
    description: '中国最美的六大草原之一，夏季绿草如茵，牛羊成群，野花遍地，宛如一幅壮美的田园画卷，是理想的避暑胜地。',
    imageUrl: 'https://space.coze.cn/api/coze_space/gen_image?image_size=landscape_16_9&prompt=Qilian%20Mountain%20grassland%20green%20pasture&sign=26c3e568f136efecfe52905e08f427fe',
    location: '青海省海北藏族自治州',
    bestSeason: '6-8月'
  }
];

export default function AttractionsSection() {
  const [visibleCards, setVisibleCards] = useState<number[]>([]);
  const cardRefs = attractionsData.map(() => ({ current: null }));
  
  // 检测卡片是否在视口中
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry, index) => {
          if (entry.isIntersecting && !visibleCards.includes(index)) {
            setVisibleCards(prev => [...prev, index]);
          }
        });
      },
      { threshold: 0.1 }
    );
    
    cardRefs.forEach((ref, index) => {
      if (ref.current) {
        observer.observe(ref.current);
      }
    });
    
    return () => {
      cardRefs.forEach(ref => {
        if (ref.current) {
          observer.unobserve(ref.current);
        }
      });
    };
  }, [visibleCards]);
  
  return (
    <section className="py-20 px-4 bg-gradient-to-b from-blue-50 to-white">
      <div className="container mx-auto max-w-6xl">
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-bold text-gray-800 mb-4">
            <i className="fa-solid fa-map-marker-alt text-blue-600 mr-2"></i>青海旅游景点
          </h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            青海拥有丰富的自然和人文景观，从湛蓝的湖泊到壮观的草原，从古老的寺庙到神秘的盐湖，每一处都令人流连忘返。
          </p>
        </motion.div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {attractionsData.map((attraction, index) => (
            <motion.div
              key={attraction.id}
              ref={el => cardRefs[index].current = el}
              initial={{ opacity: 0, y: 50 }}
              animate={visibleCards.includes(index) ? { opacity: 1, y: 0 } : {}}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              className="group"
            >
              <div className="rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-shadow duration-300 bg-white">
                <div className="relative h-56 overflow-hidden">
                  <img 
                    src={attraction.imageUrl} 
                    alt={attraction.name}
                    className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
                  />
                  <div className="absolute top-3 right-3 bg-blue-600 text-white text-xs font-bold px-2 py-1 rounded-full">
                    {attraction.bestSeason}最佳
                  </div>
                </div>
                <div className="p-5">
                  <div className="flex justify-between items-start mb-2">
                    <h3 className="text-xl font-bold text-gray-800">{attraction.name}</h3>
                    <span className="text-sm text-gray-500 flex items-center">
                      <i className="fa-solid fa-location-dot mr-1 text-blue-500"></i>
                      {attraction.location}
                    </span>
                  </div>
                  <p className="text-gray-600 mb-4">{attraction.description}</p>
                  <button className="w-full bg-blue-50 hover:bg-blue-100 text-blue-700 font-medium py-2 rounded-lg transition-colors duration-300 flex items-center justify-center">
                    <span>了解更多</span>
                    <i className="fa-solid fa-arrow-right ml-2 text-sm"></i>
                  </button>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
}