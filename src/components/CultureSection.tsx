import { motion, useScroll, useTransform } from 'framer-motion';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/Card';

// 人文故事数据
const cultureStories = [
  {
    id: 1,
    title: '藏族文化',
    description: '青海是藏族聚居区之一，藏族文化在这里得到了很好的传承和发展。藏族人民的生活习俗、宗教信仰、艺术形式等都具有独特的魅力。',
    content: '藏族是青海人口最多的少数民族之一，主要分布在玉树、果洛、海南、黄南等地区。藏族人民信仰藏传佛教，有着悠久的历史和灿烂的文化。藏族的唐卡绘画、藏戏、民间歌舞等艺术形式独具特色，展现了藏族人民的智慧和创造力。藏族的传统节日如藏历新年、雪顿节等，充满了浓郁的民族特色。',
    imageUrl: 'https://space.coze.cn/api/coze_space/gen_image?image_size=landscape_16_9&prompt=Tibetan%20culture%20traditional%20dance%20costume&sign=396291efbcb74d7f5488bf9c9079ccbe'
  },
  {
    id: 2,
    title: '丝绸之路',
    description: '青海是古丝绸之路的重要通道，历史上曾是东西方文化交流的重要枢纽，留下了丰富的历史遗迹和文化遗产。',
    content: '青海地处丝绸之路南道和唐蕃古道的交汇处，是古代中原与西域、吐蕃进行经济文化交流的重要通道。著名的丝绸之路南线从长安出发，经过青海湖、柴达木盆地，通往西域。这条古道上留下了许多古城遗址、佛教寺庙和石窟艺术，见证了古代东西方文化交流的辉煌历史。',
    imageUrl: 'https://space.coze.cn/api/coze_space/gen_image?image_size=landscape_16_9&prompt=Silk%20Road%20historical%20route%20ancient%20path&sign=aae91969fd6348acf5d6fa72497b7c7e'
  },
  {
    id: 3,
    title: '多民族和谐共处',
    description: '青海是一个多民族聚居的地区，汉、藏、回、蒙古、撒拉等多个民族在这里和谐共处，形成了独特的多元文化。',
    content: '青海境内生活着汉族、藏族、回族、蒙古族、撒拉族等多个民族，各民族在长期的历史发展中相互学习、相互融合，形成了独特的多元文化景观。不同民族的语言文字、宗教信仰、风俗习惯在这里交相辉映，共同构成了青海丰富多彩的文化画卷。这种多民族和谐共处的局面，是青海文化的重要特点。',
    imageUrl: 'https://space.coze.cn/api/coze_space/gen_image?image_size=landscape_16_9&prompt=Qinghai%20multi-ethnic%20harmony%20culture%20festival&sign=99e7cd3992a39c0581124349fc7f5916'
  }
];

export default function CultureSection() {
  return (
    <section className="py-20 px-4 bg-gradient-to-b from-green-50 to-blue-50">
      <div className="container mx-auto max-w-6xl">
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-bold text-gray-800 mb-4">
            <i className="fa-solid fa-book-open text-purple-600 mr-2"></i>青海人文故事
          </h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            青海历史悠久，文化底蕴深厚，是多民族文化的交汇之地，每一个故事都承载着这片土地的记忆和情感。
          </p>
        </motion.div>
        
        <div className="space-y-16">
          {cultureStories.map((story, index) => (
            <motion.div
              key={story.id}
              initial={{ opacity: 0, x: index % 2 === 0 ? -50 : 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true, amount: 0.3 }}
            >
              <div className={`grid grid-cols-1 lg:grid-cols-2 gap-8 items-center ${index % 2 === 1 ? 'lg:flex-row-reverse' : ''}`}>
                <div className="order-2 lg:order-none">
                  <h3 className="text-2xl font-bold text-gray-800 mb-4">{story.title}</h3>
                  <p className="text-gray-600 mb-6">{story.content}</p>
                  <div className="flex items-center text-purple-600">
                    <div className="w-10 h-10 rounded-full bg-purple-100 flex items-center justify-center mr-3">
                      <i className="fa-solid fa-history"></i>
                    </div>
                    <div>
                      <p className="text-sm font-medium">深厚历史文化底蕴</p>
                      <p className="text-xs text-gray-500">传承千年的文化遗产</p>
                    </div>
                  </div>
                </div>
                <div className="order-1 lg:order-none rounded-2xl overflow-hidden shadow-xl">
                  <img 
                    src={story.imageUrl} 
                    alt={story.title}
                    className="w-full h-[300px] md:h-[400px] object-cover"
                  />
                </div>
              </div>
            </motion.div>
          ))}
        </div>
        
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="mt-20 bg-white rounded-2xl shadow-lg p-8 md:p-10"
        >
          <div className="text-center max-w-3xl mx-auto">
            <h3 className="text-2xl font-bold text-gray-800 mb-4">探索青海的人文魅力</h3>
            <p className="text-gray-600 mb-6">
              青海不仅有壮丽的自然风光，更有深厚的人文底蕴。这里的每一个故事都值得我们去聆听，每一种文化都值得我们去尊重和传承。
            </p>
            <button className="bg-purple-600 hover:bg-purple-700 text-white font-medium py-3 px-6 rounded-full transition-colors duration-300 shadow-md hover:shadow-lg">
              开始您的青海文化之旅
            </button>
          </div>
        </motion.div>
      </div>
    </section>
  );
}