import { motion } from 'framer-motion';

interface HeaderProps {
  scrolled: boolean;
}

export default function Header({ scrolled }: HeaderProps) {
  return (
    <header 
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
        scrolled 
          ? 'bg-white/95 backdrop-blur-sm shadow-md py-2' 
          : 'bg-transparent py-4'
      }`}
    >
      <div className="container mx-auto px-4 flex justify-between items-center">
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5 }}
        >
          <h1 className={`text-2xl font-bold ${scrolled ? 'text-blue-700' : 'text-white'}`}>
            <i className="fa-solid fa-mountain mr-2"></i>青海旅游
          </h1>
        </motion.div>
        
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5 }}
        >
          <button className={`rounded-full p-2 ${scrolled ? 'text-blue-700' : 'text-white'}`}>
            <i className="fa-solid fa-info-circle text-xl"></i>
          </button>
        </motion.div>
      </div>
    </header>
  );
}