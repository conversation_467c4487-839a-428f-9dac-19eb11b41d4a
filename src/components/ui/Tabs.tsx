import React, { useState, useEffect } from "react";
import { cn } from "@/lib/utils";
import { motion } from "framer-motion";

interface TabsProps extends React.HTMLAttributes<HTMLDivElement> {
  defaultValue?: string;
  value?: string;
  onValueChange?: (value: string) => void;
  className?: string;
}

export function Tabs({
  defaultValue,
  value,
  onValueChange,
  className,
  children,
}: TabsProps) {
  const [activeValue, setActiveValue] = useState(defaultValue || "");
  const [isControlled, setIsControlled] = useState(!!value);

  useEffect(() => {
    if (value !== undefined) {
      setIsControlled(true);
      setActiveValue(value);
    } else {
      setIsControlled(false);
    }
  }, [value]);

  const handleValueChange = (newValue: string) => {
    if (!isControlled) {
      setActiveValue(newValue);
    }
    onValueChange?.(newValue);
  };

  return (
    <div className={cn("w-full", className)}>
      {React.Children.map(children, (child) => {
        if (React.isValidElement(child)) {
          if (child.type === TabsList) {
            return React.cloneElement(child, {
              activeValue,
              onValueChange: handleValueChange,
            });
          } else if (child.type === TabsContent) {
            return React.cloneElement(child, {
              value: child.props.value,
              active: child.props.value === activeValue,
            });
          }
        }
        return child;
      })}
    </div>
  );
}

interface TabsListProps extends React.HTMLAttributes<HTMLDivElement> {
  activeValue: string;
  onValueChange: (value: string) => void;
  className?: string;
}

export function TabsList({
  activeValue,
  onValueChange,
  className,
  children,
}: TabsListProps) {
  return (
    <div className={cn("flex rounded-lg bg-gray-100 p-1", className)}>
      {React.Children.map(children, (child) => {
        if (React.isValidElement(child) && child.type === TabsTrigger) {
          return React.cloneElement(child, {
            active: child.props.value === activeValue,
            onSelect: () => onValueChange(child.props.value),
          });
        }
        return child;
      })}
    </div>
  );
}

interface TabsTriggerProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  value: string;
  active: boolean;
  onSelect: () => void;
  className?: string;
}

export function TabsTrigger({
  value,
  active,
  onSelect,
  className,
  children,
}: TabsTriggerProps) {
  return (
    <button
      type="button"
      className={cn(
        "px-4 py-2 text-sm font-medium rounded-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",
        active
          ? "bg-white text-gray-900 shadow-sm"
          : "text-gray-600 hover:text-gray-900 hover:bg-gray-200",
        className
      )}
      onClick={onSelect}
    >
      {children}
    </button>
  );
}

interface TabsContentProps extends React.HTMLAttributes<HTMLDivElement> {
  value: string;
  active: boolean;
  className?: string;
}

export function TabsContent({
  value,
  active,
  className,
  children,
}: TabsContentProps) {
  return (
    <motion.div
      initial={{ opacity: 0, height: 0 }}
      animate={active ? { opacity: 1, height: "auto" } : { opacity: 0, height: 0 }}
      transition={{ duration: 0.3, ease: "easeInOut" }}
      className={cn("mt-2", className)}
    >
      {active && children}
    </motion.div>
  );
}