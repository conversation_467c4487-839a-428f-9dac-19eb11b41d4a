import { motion } from 'framer-motion';

type Section = 'attractions' | 'noodle' | 'cuisine' | 'culture';

interface SectionNavProps {
  activeSection: Section;
  onSectionChange: (section: Section) => void;
  scrolled: boolean;
}

export default function SectionNav({ activeSection, onSectionChange, scrolled }: SectionNavProps) {
  // 板块信息映射
  const sections = [
    { id: 'attractions', label: '旅游景点' },
    { id: 'noodle', label: '拉面特色' },
    { id: 'cuisine', label: '地方美食' },
    { id: 'culture', label: '人文故事' },
  ];
  
  return (
    <motion.nav
      className={`fixed top-16 left-0 right-0 z-40 transition-all duration-300 ${
        scrolled 
          ? 'bg-white/90 backdrop-blur-sm shadow-sm py-2' 
          : 'bg-transparent py-3'
      }`}
    >
      <div className="container mx-auto">
        <ul className="flex justify-center space-x-1 md:space-x-6 text-sm md:text-base">
          {sections.map((section) => (
            <li key={section.id}>
              <button
                onClick={() => onSectionChange(section.id as Section)}
                className={`px-3 py-2 rounded-full transition-all ${
                  activeSection === section.id 
                    ? 'bg-blue-600 text-white font-medium shadow-md' 
                    : `${scrolled ? 'text-gray-700 hover:bg-gray-100' : 'text-white/90 hover:text-white'}`
                }`}
              >
                {section.label}
              </button>
            </li>
          ))}
        </ul>
      </div>
    </motion.nav>
  );
}