import { motion } from 'framer-motion';
import { <PERSON>, Card<PERSON>ontent, CardFooter, CardHeader, CardTitle } from '@/components/ui/Card';

// 地方美食数据
const cuisineData = [
  {
    id: 1,
    name: '手抓羊肉',
    description: '青海传统名菜，选用优质羊肉，清水煮熟后直接用手抓食，肉质鲜嫩，肥而不腻，是招待贵客的上等菜肴。',
    imageUrl: 'https://space.coze.cn/api/coze_space/gen_image?image_size=landscape_16_9&prompt=Qinghai%20hand%20grabbed%20mutton%20traditional%20food&sign=ecf22144f0aa811b2e83601b00e48cf0',
    origin: '蒙古族、藏族传统美食'
  },
  {
    id: 2,
    name: '酿皮',
    description: '青海特色小吃，以面粉为原料制成凉皮，配以醋、辣油、芝麻酱等调料，口感爽滑，酸辣开胃，是夏季消暑佳品。',
    imageUrl: 'https://space.coze.cn/api/coze_space/gen_image?image_size=landscape_16_9&prompt=Qinghai%20niangpi%20cold%20noodle%20snack&sign=03a5f11af462609a23bdabca072d463c',
    origin: '回族特色小吃'
  },
  {
    id: 3,
    name: '甜醅',
    description: '又称酒醅，以青稞或燕麦为原料发酵制成，味道甘甜，带有轻微酒香，营养丰富，具有开胃助消化的功效。',
    imageUrl: 'https://space.coze.cn/api/coze_space/gen_image?image_size=landscape_16_9&prompt=Qinghai%20sweet%20fermented%20grains%20snack&sign=995d679c84129627e105c72ea8c127e2',
    origin: '藏族传统食品'
  },
  {
    id: 4,
    name: '糌粑',
    description: '藏族传统主食，将青稞炒熟后磨成粉，用酥油茶或青稞酒拌和后捏成团食用，营养丰富，便于携带。',
    imageUrl: 'https://space.coze.cn/api/coze_space/gen_image?image_size=landscape_16_9&prompt=Tibetan%20tsampa%20traditional%20food&sign=bcf0714f7ec63f4c98ed89697383f664',
    origin: '藏族传统主食'
  },
  {
    id: 5,
    name: '酸奶',
    description: '青海特色酸奶，以牦牛奶或牛奶为原料，自然发酵而成，质地浓稠，味道酸甜可口，富含益生菌。',
    imageUrl: 'https://space.coze.cn/api/coze_space/gen_image?image_size=landscape_16_9&prompt=Qinghai%20traditional%20yogurt%20food&sign=a3c1a27ab97b432665d22c6de2b0ea63',
    origin: '多民族传统食品'
  },
  {
    id: 6,
    name: '炕锅羊肉',
    description: '青海特色菜肴，将羊肉、土豆、洋葱等食材在特制的锅中炒制，锅底焦香，肉质鲜嫩，香气扑鼻。',
    imageUrl: 'https://space.coze.cn/api/coze_space/gen_image?image_size=landscape_16_9&prompt=Qinghai%20kang%20pot%20mutton%20delicious%20food&sign=ce190e4dc52113ce08955734390fc530',
    origin: '青海特色创新菜'
  }
];

export default function CuisineSection() {
  return (
    <section className="py-20 px-4 bg-gradient-to-b from-orange-50 to-green-50">
      <div className="container mx-auto max-w-6xl">
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-bold text-gray-800 mb-4">
            <i className="fa-solid fa-utensils text-green-600 mr-2"></i>青海地方美食
          </h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            青海美食融合了汉、藏、回、蒙古等多个民族的饮食特色，口味独特，风味各异，每一道菜肴都讲述着一个民族的故事。
          </p>
        </motion.div>
        
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
          {cuisineData.map((cuisine, index) => (
            <motion.div
              key={cuisine.id}
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="group"
            >
              <Card className="overflow-hidden h-full hover:shadow-xl transition-all duration-300 border-none shadow-lg">
                <div className="relative h-48 overflow-hidden">
                  <img 
                    src={cuisine.imageUrl} 
                    alt={cuisine.name}
                    className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
                  />
                </div>
                <CardHeader className="pb-2">
                  <div className="flex justify-between items-start">
                    <CardTitle className="text-xl">{cuisine.name}</CardTitle>
                    <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">
                      {cuisine.origin}
                    </span>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600 text-sm">{cuisine.description}</p>
                </CardContent>
                <CardFooter className="pt-0">
                  <button className="text-green-600 hover:text-green-700 font-medium text-sm flex items-center">
                    <span>了解更多</span>
                    <i className="fa-solid fa-arrow-right ml-1 text-xs"></i>
                  </button>
                </CardFooter>
              </Card>
            </motion.div>
          ))}
        </div>
        
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.5 }}
          viewport={{ once: true }}
          className="mt-16 text-center"
        >
          <div className="inline-block bg-white p-6 rounded-xl shadow-md max-w-2xl">
            <h3 className="text-xl font-bold text-gray-800 mb-3">青海美食文化特色</h3>
            <p className="text-gray-600">
              青海美食以牛羊肉、青稞、乳制品为主要原料，口味偏重，注重原汁原味。由于地处高原，青海饮食还具有高热量、高营养的特点，以适应高原环境。
              各民族的饮食文化相互交融，形成了独特的青海美食体系。
            </p>
          </div>
        </motion.div>
      </div>
    </section>
  );
}