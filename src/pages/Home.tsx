import { useState, useEffect, useRef } from 'react';
import { motion, useScroll, useTransform } from 'framer-motion';
import Header from '@/components/Header';
import SectionNav from '@/components/SectionNav';
import AttractionsSection from '@/components/AttractionsSection';
import NoodleSection from '@/components/NoodleSection';
import CuisineSection from '@/components/CuisineSection';
import CultureSection from '@/components/CultureSection';
import Footer from '@/components/Footer';

// 定义板块类型
type Section = 'attractions' | 'noodle' | 'cuisine' | 'culture';

export default function Home() {
  const [activeSection, setActiveSection] = useState<Section>('attractions');
  const [scrolled, setScrolled] = useState(false);
  
  // 板块引用
  const attractionsRef = useRef<HTMLDivElement>(null);
  const noodleRef = useRef<HTMLDivElement>(null);
  const cuisineRef = useRef<HTMLDivElement>(null);
  const cultureRef = useRef<HTMLDivElement>(null);
  
  // 监听滚动事件，更新当前活动板块
  useEffect(() => {
    const handleScroll = () => {
      // 更改导航栏样式
      setScrolled(window.scrollY > 50);
      
      // 确定当前可见板块
      const scrollPosition = window.scrollY + 100;
      
      if (cultureRef.current && scrollPosition >= cultureRef.current.offsetTop) {
        setActiveSection('culture');
      } else if (cuisineRef.current && scrollPosition >= cuisineRef.current.offsetTop) {
        setActiveSection('cuisine');
      } else if (noodleRef.current && scrollPosition >= noodleRef.current.offsetTop) {
        setActiveSection('noodle');
      } else if (attractionsRef.current && scrollPosition >= attractionsRef.current.offsetTop) {
        setActiveSection('attractions');
      }
    };
    
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);
  
  // 滚动到指定板块
  const scrollToSection = (section: Section) => {
    const refMap: Record<Section, React.RefObject<HTMLDivElement>> = {
      attractions: attractionsRef,
      noodle: noodleRef,
      cuisine: cuisineRef,
      culture: cultureRef,
    };
    
    const element = refMap[section].current;
    if (element) {
      window.scrollTo({
        top: element.offsetTop - 80,
        behavior: 'smooth'
      });
      setActiveSection(section);
    }
  };
  
  return (
    <div className="min-h-screen bg-gradient-to-b from-blue-50 to-white text-gray-800 font-sans">
      {/* 头部区域 */}
      <Header scrolled={scrolled} />
      
      {/* 导航栏 */}
      <SectionNav 
        activeSection={activeSection} 
        onSectionChange={scrollToSection} 
        scrolled={scrolled}
      />
      
      {/* 主横幅 */}
      <div className="relative h-[80vh] min-h-[500px] w-full overflow-hidden">
        <div 
          className="absolute inset-0 bg-cover bg-center"
          style={{ 
            backgroundImage: 'url(https://space.coze.cn/api/coze_space/gen_image?image_size=landscape_16_9&prompt=Qinghai%20Lake%20scenery%20blue%20water%20mountain%20background&sign=5ef7c840e05ac2171fba6770f801d883)' 
          }}
        >
          <div className="absolute inset-0 bg-black/30"></div>
        </div>
        
        <motion.div 
          className="relative h-full flex flex-col justify-center items-center text-white text-center px-4"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 1 }}
        >
          <h1 className="text-4xl md:text-6xl font-bold mb-4 tracking-tight">青海旅游</h1>
          <p className="text-xl md:text-2xl max-w-2xl mx-auto mb-8">
            探索青藏高原的明珠，体验独特的自然风光与人文魅力
          </p>
          <button 
            onClick={() => scrollToSection('attractions')}
            className="bg-white text-blue-700 hover:bg-blue-50 px-8 py-3 rounded-full font-medium transition-all transform hover:scale-105 shadow-lg"
          >
            开始探索 <i className="fa-solid fa-arrow-down ml-2"></i>
          </button>
        </motion.div>
        
        {/* 向下滚动指示器 */}
        <motion.div 
          className="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-white animate-bounce"
          onClick={() => scrollToSection('attractions')}
        >
          <i className="fa-solid fa-chevron-down text-2xl cursor-pointer"></i>
        </motion.div>
      </div>
      
      {/* 旅游景点板块 */}
      <div ref={attractionsRef}>
        <AttractionsSection />
      </div>
      
      {/* 拉面特色板块 */}
      <div ref={noodleRef}>
        <NoodleSection />
      </div>
      
      {/* 地方美食板块 */}
      <div ref={cuisineRef}>
        <CuisineSection />
      </div>
      
      {/* 人文故事板块 */}
      <div ref={cultureRef}>
        <CultureSection />
      </div>
      
      {/* 页脚 */}
      <Footer />
    </div>
  );
}